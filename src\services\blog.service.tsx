import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class BlogService {
  // Get all blogs (no pagination on API side)
  static async getAllBlogs() {
    const response = await makeRequest("admin/blogs", RequestMethods.GET);
    return response;
  }


  // Get single blog by ID
  static async getBlogById(id: string) {
    const response = await makeRequest(`admin/blogs/${id}`, RequestMethods.GET);
    return response;
  }

  // Create new blog
  static async createBlog(blogData: any) {
    return await makeRequest("admin/blogs", RequestMethods.POST, blogData);
  }

  // Update existing blog by ID
  static async updateBlogById(id: string, blogData: any) {
    return await makeRequest(`admin/blogs/${id}`, RequestMethods.PUT, blogData);
  }

  // Delete blog by ID
  static async deleteBlog(id: string) {
    return await makeRequest(`admin/blogs/${id}`, RequestMethods.DELETE);
  }

  // Upload blog cover image
  static async uploadBlogImage(file: File) {
    const formData = new FormData();
    formData.append("image", file);

    const response = await makeRequest("admin/blogs/upload/blog-image", RequestMethods.POST, formData);
    return response;
  }

  // Upload writer image
  static async uploadWriterImage(file: File) {
    const formData = new FormData();
    formData.append("image", file);

    const response = await makeRequest("/blogs/upload/writer-image", RequestMethods.POST, formData);
    return response;
  }

  // Delete image
  static async deleteImage(imageUrl: string) {
    const response = await makeRequest("/blogs/image/delete", RequestMethods.DELETE, { imageUrl });
    return response;
  }

  // Legacy upload file method (keeping for backward compatibility)
  static async uploadFile(file: File) {
    return this.uploadBlogImage(file);
  }
}
