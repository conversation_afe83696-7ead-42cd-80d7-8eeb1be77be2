import React, { useEffect, useState } from "react";
import { JournalService } from "../../services/journal.service";

interface Blog {
  _id: string;
  blogName: string;
  subheading?: string;
  description: string;
  handle: string;
  featureImage: string;
  writerName: string;
  isVisible: boolean;
  publishDate: string;
}

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
  };
  position: number;
}

interface JournalAddModalProps {
  show: boolean;
  onHide: () => void;
  onAdd: (selectedBlogs: Blog[]) => void;
  existingJournals: JournalEntry[];
}

const JournalAddModal: React.FC<JournalAddModalProps> = ({
  show,
  onHide,
  onAdd,
  existingJournals
}) => {
  const [blogs, setBlogs] = useState<Blog[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedBlogs, setSelectedBlogs] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (show) {
      fetchBlogs();
      // Pre-select already added blogs
      const existingBlogIds = existingJournals.map(journal => journal.blogID._id);
      setSelectedBlogs(existingBlogIds);
      setSearchTerm("");
    }
  }, [show, existingJournals]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get all blogs for journal selection
      const response = await JournalService.getAllBlogsForSelection();

      if (response.data && response.data.success) {
        const blogData = response.data.data;
        if (Array.isArray(blogData)) {
          setBlogs(blogData);
        } else if (blogData && Array.isArray(blogData.blogs)) {
          setBlogs(blogData.blogs);
        } else {
          setBlogs([]);
        }
      } else {
        throw new Error(response.data?.message || "Failed to fetch blogs");
      }
    } catch (error: any) {
      setError(error.response?.data?.message || error.message || "Failed to fetch blogs");
      setBlogs([]);
    } finally {
      setLoading(false);
    }
  };

  const handleBlogSelection = (blogId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedBlogs(prev => [...prev, blogId]);
    } else {
      setSelectedBlogs(prev => prev.filter(id => id !== blogId));
    }
  };

  const getAllBlogs = () => {
    return blogs; // Return all blogs, not filtered
  };

  const isExistingBlog = (blogId: string) => {
    const existingBlogIds = existingJournals.map(journal => journal.blogID._id);
    return existingBlogIds.includes(blogId);
  };

  const getFilteredBlogs = () => {
    const allBlogs = getAllBlogs();
    if (!searchTerm) return allBlogs;

    return allBlogs.filter((blog: Blog) =>
      blog.blogName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.writerName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      blog.description?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const handleAdd = () => {
    // Send all currently selected blogs (both existing and new)
    const selectedBlogObjects = blogs.filter(blog => selectedBlogs.includes(blog._id));

    onAdd(selectedBlogObjects);
    onHide();
  };

  const filteredBlogs = getFilteredBlogs();

  if (!show) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-full items-center justify-center p-2 sm:p-4">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onHide}
        ></div>

        {/* Modal panel */}
        <div className="relative transform overflow-hidden rounded-lg bg-white shadow-xl transition-all w-full max-w-xs sm:max-w-2xl lg:max-w-4xl max-h-[80vh] sm:max-h-[75vh] flex flex-col">
          {/* Header */}
          <div className="bg-white px-4 sm:px-6 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg sm:text-xl font-semibold text-gray-900">Select Blog Articles</h3>
              <button
                onClick={onHide}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Search Section */}
          <div className="px-4 sm:px-6 py-2 border-b border-gray-200 bg-gray-50">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search blogs by title, author, or content..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm"
              />
            </div>
          </div>

          {/* Body */}
          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">Loading blogs...</span>
              </div>
            ) : error ? (
              <div className="p-6">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                  <h4 className="text-red-800 font-medium mb-2">Error Loading Blogs</h4>
                  <p className="text-red-600 text-sm mb-3">{error}</p>
                  <button
                    onClick={fetchBlogs}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              <>
                {/* Blog Selection List */}
                {filteredBlogs.length === 0 ? (
                  <div className="p-6 text-center">
                    <div className="text-gray-500 py-8">
                      <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-lg font-medium text-gray-900 mb-1">No blogs available</p>
                      <p className="text-sm text-gray-500">
                        {blogs.length === 0
                          ? "No blogs available in the system."
                          : "No blogs found matching your search."
                        }
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="p-3 sm:p-4">
                    <div className="space-y-2">
                      {filteredBlogs.map((blog) => (
                        <div
                          key={blog._id}
                          className={`border rounded-lg p-2.5 cursor-pointer transition-all duration-200 hover:shadow-md ${
                            selectedBlogs.includes(blog._id)
                              ? 'border-blue-500 bg-blue-50 shadow-sm'
                              : 'border-gray-200 hover:border-gray-300 bg-white'
                          }`}
                          onClick={() => handleBlogSelection(blog._id, !selectedBlogs.includes(blog._id))}
                        >
                          <div className="flex items-start space-x-3">
                            <input
                              type="checkbox"
                              checked={selectedBlogs.includes(blog._id)}
                              onChange={(e) => handleBlogSelection(blog._id, e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                              onClick={(e) => e.stopPropagation()}
                            />

                            <div className="flex-1 min-w-0">
                              <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between">
                                <div className="flex-1 sm:pr-3">
                                  <p className="text-sm font-medium text-gray-900 mb-0.5">
                                    {blog.writerName || "Unknown Author"}
                                  </p>
                                  <h4 className="text-sm font-semibold text-gray-800 mb-0.5 leading-tight line-clamp-1">
                                    {blog.blogName}
                                  </h4>
                                  <div className="text-xs text-gray-600 line-clamp-1">
                                    <div dangerouslySetInnerHTML={{
                                      __html: blog.description?.replace(/<[^>]*>/g, '').substring(0, 80) + "..." || "No description available"
                                    }} />
                                  </div>
                                </div>

                                {/* Featured Seal positioned between content and image */}
                                {isExistingBlog(blog._id) && (
                                  <div className="flex-shrink-0 flex items-center justify-center mx-2">
                                    <div className="relative">
                                      {/* Seal/Badge SVG */}
                                      <svg width="48" height="48" viewBox="0 0 48 48" className="text-blue-600">
                                        <circle cx="24" cy="24" r="22" fill="currentColor" stroke="#1e40af" strokeWidth="2"/>
                                        <circle cx="24" cy="24" r="18" fill="none" stroke="#ffffff" strokeWidth="1" strokeDasharray="2,2"/>
                                      </svg>
                                      {/* Featured text */}
                                      <div className="absolute inset-0 flex items-center justify-center">
                                        <span className="text-white text-[8px] font-bold leading-tight text-center">
                                          FEATURED
                                        </span>
                                      </div>
                                    </div>
                                  </div>
                                )}

                                <div className="flex-shrink-0 mt-1 sm:mt-0 sm:ml-3">
                                  {blog.featureImage ? (
                                    <img
                                      src={blog.featureImage}
                                      alt={blog.blogName}
                                      className="w-full h-16 sm:w-16 sm:h-14 object-cover rounded border border-gray-200"
                                    />
                                  ) : (
                                    <div className="w-full h-16 sm:w-16 sm:h-14 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                      </svg>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 sm:px-6 py-4 flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-3 border-t border-gray-200">
            <button
              onClick={onHide}
              className="w-full sm:w-auto px-4 sm:px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
            >
              Discard
            </button>
            <button
              onClick={handleAdd}
              disabled={selectedBlogs.length === 0 || loading}
              className="w-full sm:w-auto px-4 sm:px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JournalAddModal;
