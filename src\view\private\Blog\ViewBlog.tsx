import React, { useState, useRef, useEffect } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useNavigate, useParams } from "react-router-dom";
import toast from "react-hot-toast";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import { BlogService } from "../../../services/blog.service";

// Note: Now using real API instead of dummy data

export default function ViewBlog() {
  const navigate = useNavigate();
  const { id, handle } = useParams<{ id?: string; handle?: string }>();
  const coverFileInputRef = useRef<HTMLInputElement>(null);
  const writerFileInputRef = useRef<HTMLInputElement>(null);

  const [loading, setLoading] = useState(true);
  const [blogFetchError, setBlogFetchError] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState("");
  const [subheading, setSubheading] = useState("");
  const [content, setContent] = useState("");
  const [isPublished, setIsPublished] = useState(false);
  const [image, setImage] = useState("");
  const [imageError, setImageError] = useState("");
  const [blogHandle, setBlogHandle] = useState("");
  const [writerName, setWriterName] = useState("");
  const [writerShortName, setWriterShortName] = useState("");
  const [writerDesignation, setWriterDesignation] = useState("");
  const [writerImage, setWriterImage] = useState("");
  const [writerImageError, setWriterImageError] = useState("");
  const [tag, setTag] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [editSaveError, setEditSaveError] = useState("");
  const [coverImageLoading, setCoverImageLoading] = useState(false);
  const [writerImageLoading, setWriterImageLoading] = useState(false);
  const [saveModal, setSaveModal] = useState({
    isOpen: false,
    loading: false
  });

  useEffect(() => {
    const fetchBlog = async () => {
      if (!id && !handle) return;

      try {
        setLoading(true);
        setBlogFetchError("");

        let response: any;
        if (handle) {
          console.log("Fetching blog with handle:", handle);
          response = await BlogService.getBlogByHandle(handle);
        } else if (id) {
          console.log("Fetching blog with ID:", id);
          response = await BlogService.getBlogById(id);
        }

        if (!response || !response.data || !response.data.success) {
          throw new Error(response?.data?.message || "Failed to fetch blog details");
        }

        const blog = response.data.data;

        setTitle(blog.blogName || "");
        setSubheading(blog.blogSubheading || "");
        setContent(blog.description || "");
        setIsPublished(blog.isVisible || false);
        setImage(blog.featureImage || "");
        setBlogHandle(blog.handle || "");
        setWriterName(blog.writerName || "");
        setWriterShortName(blog.writerShortname || "");
        setWriterDesignation(blog.writerDesignation || "");
        setWriterImage(blog.writerImage || "");

        // Handle tags - ensure it's always an array
        const blogTags = blog.tag || blog.tags || [];
        setTags(Array.isArray(blogTags) ? blogTags : []);
      } catch (error: any) {
        setBlogFetchError(error.response?.data?.message || error.message || "Failed to fetch blog details");
      } finally {
        setLoading(false);
      }
    };

    fetchBlog();
  }, [id, handle]);

  // Start in view mode when accessed via edit route
  useEffect(() => {
    if (handle) {
      setIsEditing(false);
    }
  }, [handle]);

  // Helper function to format handle for SEO
  const formatHandle = (value: string) => {
    return value
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  };

  // Handle handle input change with formatting
  const handleHandleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedHandle = formatHandle(e.target.value);
    setBlogHandle(formattedHandle);
  };

  const uploadImageToS3 = async (file: File, imageType: string = "blog") => {
    try {
      let response: any;
      if (imageType === "writer") {
        response = await BlogService.uploadWriterImage(file);
      } else {
        response = await BlogService.uploadBlogImage(file);
      }

      if (response.data && response.data.success) {
        return {
          success: true,
          url: response.data.data.url || response.data.url
        };
      } else {
        return {
          success: false,
          message: response.data?.message || "Upload failed"
        };
      }
    } catch (error: any) {
      console.error("File upload error:", error);
      return {
        success: false,
        message: error.response?.data?.message || "Upload failed"
      };
    }
  };

  // Handle Image Upload
  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    setImageFunction: (url: string) => void,
    triggerBy: string
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      setImageError("File size should not exceed 5MB");
      return;
    }

    if (triggerBy === "coverImage") setCoverImageLoading(true);
    if (triggerBy === "writerImage") setWriterImageLoading(true);

    const imageType = triggerBy === "writerImage" ? "writer" : "blog";
    const imageUrl = await uploadImageToS3(file, imageType);
    if (imageUrl.success) {
      setImageFunction(imageUrl.url!);
      setImageError("");
      setWriterImageError("");
    } else {
      if (triggerBy === "coverImage") {
        setImageError(imageUrl.message || "Upload failed");
      } else if (triggerBy === "writerImage") {
        setWriterImageError(imageUrl.message || "Upload failed");
      }
    }

    if (triggerBy === "coverImage") setCoverImageLoading(false);
    if (triggerBy === "writerImage") setWriterImageLoading(false);
  };

  // Handle Tag Addition
  const handleAddTag = () => {
    const trimmedTag = tag.trim();
    console.log("Adding tag:", trimmedTag);
    console.log("Current tags:", tags);

    if (trimmedTag && !tags.includes(trimmedTag)) {
      const newTags = [...tags, trimmedTag];
      console.log("New tags array:", newTags);
      setTags(newTags);
      setTag("");
      setShowDropdown(false);
    } else {
      console.log("Tag not added - either empty or already exists");
    }
  };

  // Handle Enter key press for tag input
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle Tag Removal
  const handleRemoveTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  // Handle Image Removal
  const handleRemoveImage = (setImageFunction: (url: string) => void, triggerBy: string) => {
    setImageFunction("");
    if (triggerBy === "coverImage" && coverFileInputRef.current) {
      coverFileInputRef.current.value = "";
    } else if (triggerBy === "writerImage" && writerFileInputRef.current) {
      writerFileInputRef.current.value = "";
    }
  };

  // Handle Save Changes
  const handleSave = () => {
    setSaveModal({ isOpen: true, loading: false });
  };

  const handleSaveConfirm = async () => {
    setSaveModal(prev => ({ ...prev, loading: true }));
    try {
      setEditSaveError("");

      if (!id && !handle) {
        throw new Error("Blog ID or handle is required");
      }

      const updateData = {
        blogName: title,
        blogSubheading: subheading,
        description: content,
        isVisible: isPublished,
        featureImage: image,
        handle: blogHandle,
        writerName,
        writerShortname: writerShortName,
        writerDesignation,
        writerImage,
        tag: tags
      };



      // Use handle if available, otherwise use id
      const identifier = handle || id;
      const response = await BlogService.updateBlog(identifier, updateData);

      if (response.data && response.data.success) {
        toast.success(response.data.message || "Blog post updated successfully!");
        setIsEditing(false);
        setSaveModal({ isOpen: false, loading: false });
      } else {
        throw new Error(response.data?.message || "Failed to update blog post");
      }
    } catch (error: any) {
      console.error("Error updating blog:", error);
      const errorMessage = error.response?.data?.message || error.message || "Failed to update blog post";
      setEditSaveError(errorMessage);
      toast.error(errorMessage);
      setSaveModal({ isOpen: false, loading: false });
    }
  };

  const handleSaveCancel = () => {
    setSaveModal({ isOpen: false, loading: false });
  };

  return (
    <>
      {loading ? (
        <div className="text-center text-lg text-gray-500">Loading...</div>
      ) : blogFetchError.length > 0 ? (
        <div className="text-center text-red-500">{blogFetchError}</div>
      ) : (
        <div className="max-w-5xl mx-auto p-3 sm:p-6 space-y-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
            <div className="flex items-center gap-x-4">
              <button
                onClick={() => {
                  navigate("/blogs");
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                  width="24px"
                  height="24px"
                  color="#343C6A"
                >
                  <path
                    fillRule="evenodd"
                    d="M17 10a.75.75 0 0 1-.75.75H5.612l4.158 3.96a.75.75 0 1 1-1.04 1.08l-5.5-5.25a.75.75 0 0 1 0-1.08l5.5-5.25a.75.75 0 1 1 1.04 1.08L5.612 9.25H16.25A.75.75 0 0 1 17 10Z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
              <h1 className="text-xl sm:text-2xl font-semibold text-left w-full sm:w-64 break-words">
                {isEditing ? "Edit: " : "View: "}{title}
              </h1>
            </div>

            {!isEditing ? (
              <button
                onClick={() => setIsEditing(true)}
                className="w-full sm:w-auto p-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700"
              >
                Enable Editing
              </button>
            ) : (
              <button
                onClick={handleSave}
                className="w-full sm:w-auto p-3 bg-green-600 text-white font-medium rounded-md hover:bg-green-700"
              >
                Save Changes
              </button>
            )}
          </div>

          {editSaveError && (
            <div className="bg-red-100 border border-red-400 text-red-700 p-3 rounded-md mt-2">
              <p className="text-sm">{editSaveError}</p>
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
            <div className="col-span-2 space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-6 min-h-[650px]">
                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Title
                  </label>
                  <input
                    type="text"
                    placeholder="Give your blog post a title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Subheading
                  </label>
                  <textarea
                    placeholder="Add a brief subheading (3-4 lines)"
                    value={subheading}
                    onChange={(e) => setSubheading(e.target.value)}
                    rows={3}
                    maxLength={300}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
                    disabled={!isEditing}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {subheading.length}/300 characters
                  </p>
                </div>

                <div className="mb-20">
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Content
                  </label>
                  <ReactQuill
                    theme="snow"
                    value={content}
                    onChange={setContent}
                    className="mt-1"
                    style={{ height: "24em", marginBottom: "4rem" }}
                    readOnly={!isEditing}
                  />
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h2 className="text-lg font-medium text-left">Blog Handle</h2>
                <input
                  type="text"
                  value={blogHandle}
                  onChange={handleHandleChange}
                  placeholder="blog-handle-for-seo"
                  className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  disabled={!isEditing}
                />
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h2 className="text-lg font-medium text-left">Visibility</h2>
                <div className="flex items-center justify-between">
                  <span>{isPublished ? "Visible" : "Hidden"}</span>
                  <button
                    onClick={() => setIsPublished(!isPublished)}
                    className={`relative inline-flex items-center h-6 w-12 rounded-full transition ${
                      isPublished ? "bg-green-500" : "bg-gray-300"
                    }`}
                    disabled={!isEditing}
                  >
                    <span
                      className={`inline-block w-5 h-5 transform bg-white rounded-full transition ${
                        isPublished ? "translate-x-6" : "translate-x-1"
                      }`}
                    ></span>
                  </button>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h2 className="text-lg font-medium text-left">
                  Blog Cover Image
                </h2>
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageChange(e, setImage, "coverImage")}
                  disabled={!isEditing}
                  className="hidden"
                  id="coverImageUploadEdit"
                  ref={coverFileInputRef}
                />
                <div className="text-left">
                  <label
                    htmlFor="coverImageUploadEdit"
                    className={`inline-block bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium shadow-sm
             ${
               isEditing
                 ? "hover:bg-gray-900 hover:text-white cursor-pointer transition duration-200"
                 : ""
             } `}
                  >
                    Choose Image
                  </label>
                </div>
                {image && !coverImageLoading && (
                  <div className="relative w-fit">
                    <img
                      src={image}
                      alt="Blog Cover"
                      className="w-20 h-20 object-cover rounded-md"
                    />
                    {isEditing && (
                      <button
                        onClick={() =>
                          handleRemoveImage(setImage, "coverImage")
                        }
                        className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 bg-gray-500 text-white text-xs rounded-full p-1 shadow-md"
                      >
                        &times;
                      </button>
                    )}
                    <span className="text-[12px] text-left text-[#353535] mt-1">(Image size: 1200x600) </span>
                  </div>
                )}
                {coverImageLoading && (
                  <div className="flex items-center mt-2">
                    <span className="loader mr-2"></span>
                    <span className="text-blue-500 text-sm">Uploading image...</span>
                  </div>
                )}
                {imageError && (
                  <p className="text-red-500 text-sm">{imageError}</p>
                )}
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h2 className="text-lg font-medium text-left">
                  Writer Details
                </h2>

                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Writer Name
                  </label>
                  <input
                    type="text"
                    placeholder="Enter writer name"
                    value={writerName}
                    onChange={(e) => setWriterName(e.target.value)}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Writer Short Description
                  </label>
                  <input
                    type="text"
                    placeholder="Enter writer short description"
                    value={writerShortName}
                    onChange={(e) => setWriterShortName(e.target.value)}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Writer Designation
                  </label>
                  <input
                    type="text"
                    placeholder="Enter writer designation"
                    value={writerDesignation}
                    onChange={(e) => setWriterDesignation(e.target.value)}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    disabled={!isEditing}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 text-left">
                    Writer Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) =>
                      handleImageChange(e, setWriterImage, "writerImage")
                    }
                    disabled={!isEditing}
                    className="hidden"
                    id="writerImageUploadEdit"
                    ref={writerFileInputRef}
                  />
                  <div className="text-left">
                    <label
                      htmlFor="writerImageUploadEdit"
                      className={`inline-block bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium shadow-sm ${
                        isEditing
                          ? "hover:bg-gray-900 hover:text-white cursor-pointer transition duration-200"
                          : ""
                      }`}
                    >
                      Choose Image
                    </label>
                  </div>
                  {writerImage && !writerImageLoading && (
                    <div className="relative w-fit mt-4">
                      <img
                        src={writerImage}
                        alt="Writer"
                        className="w-20 h-20 object-cover rounded-full"
                      />
                      {isEditing && (
                        <button
                          onClick={() =>
                            handleRemoveImage(setWriterImage, "writerImage")
                          }
                          className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 bg-gray-500 text-white text-xs rounded-full p-1 shadow-md"
                        >
                          &times;
                        </button>
                      )}
                      <span className="text-[12px] text-left text-[#353535] mt-1">(Image size: 300x300) </span>
                    </div>
                  )}
                  {writerImageLoading && (
                    <div className="flex items-center mt-2">
                      <span className="loader mr-2"></span>
                      <span className="text-blue-500 text-sm">Uploading image...</span>
                    </div>
                  )}
                  {writerImageError && (
                    <p className="text-red-500 text-sm">{writerImageError}</p>
                  )}
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
                <h2 className="text-lg font-medium text-left">Tags</h2>
                <div className="space-y-2">
                  <input
                    type="text"
                    placeholder="Enter tag and press Enter or click Add"
                    value={tag}
                    onChange={(e) => {
                      setTag(e.target.value);
                      setShowDropdown(e.target.value.trim().length > 0);
                    }}
                    onKeyDown={handleTagKeyDown}
                    onFocus={() => setShowDropdown(tag.trim().length > 0)}
                    onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
                    className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                    disabled={!isEditing}
                  />

                  {/* Tag Dropdown */}
                  {showDropdown && tag.trim().length > 0 && !tags.includes(tag.trim()) && (
                    <div className="bg-white border border-gray-300 rounded-md mt-1 shadow-md z-10">
                      <button
                        type="button"
                        className="w-full text-left p-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                        onClick={handleAddTag}
                      >
                        Add "{tag.trim()}"
                      </button>
                    </div>
                  )}
                  <div className="space-y-2 mt-4">
                    <div className="mt-2 flex flex-wrap gap-2">
                      {tags.map((tag, index) => (
                        <span
                          key={index}
                          className="flex items-center px-3 py-1 rounded-full border border-gray-300 bg-gray-100 text-gray-700 text-sm"
                        >
                          {tag}
                          {isEditing && (
                            <button
                              onClick={() => handleRemoveTag(index)}
                              className="ml-2 text-gray-500 hover:text-gray-700"
                            >
                              &times;
                            </button>
                          )}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
              {isEditing && (
                <button
                  onClick={() => setIsEditing(false)}
                  className="w-full p-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Save Confirmation Modal */}
      <ConfirmationModal
        show={saveModal.isOpen}
        onHide={handleSaveCancel}
        onConfirm={handleSaveConfirm}
        title="Save Changes"
        message="Are you sure you want to save these changes to the blog post?"
        confirmText="Save Changes"
        cancelText="Cancel"
        type="info"
        loading={saveModal.loading}
      />
    </>
  );
}
