import React, { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { JournalService } from "../../../services/journal.service";
import JournalAddModal from "../../../components/Journal/JournalAddModal";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import { FaTrash } from "react-icons/fa";

interface JournalEntry {
  blogID: {
    _id: string;
    blogName: string;
    description: string;
    handle: string;
    featureImage: string;
    writerName: string;
    isVisible: boolean;
  };
  position: number;
}

interface JournalSection {
  _id: string;
  sectionType: string;
  isVisible: boolean;
  sectionTitle: string;
  journals: JournalEntry[];
}

const Journal = () => {
  const [journalSection, setJournalSection] = useState<JournalSection | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedJournalId, setSelectedJournalId] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    fetchJournalSection();
  }, [refreshKey]);

  const fetchJournalSection = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await JournalService.getJournalSection();

      if (response.data && response.data.success) {
        const sectionData = response.data.data;

        // If there are journals, fetch full blog details for each
        if (sectionData.journals && sectionData.journals.length > 0) {
          try {
            // Get all blogs to populate journal details
            const blogsResponse = await JournalService.getAllBlogsForSelection();
            if (blogsResponse.data && blogsResponse.data.success) {
              const allBlogs = blogsResponse.data.data.blogs || blogsResponse.data.data || [];

              // Map journal entries with full blog details
              const journalsWithDetails = sectionData.journals.map((journal: any) => {
                const blogDetails = allBlogs.find((blog: any) => blog._id === journal.blogID);
                return {
                  ...journal,
                  blogID: blogDetails || { _id: journal.blogID, blogName: "Blog not found" }
                };
              });

              setJournalSection({
                ...sectionData,
                journals: journalsWithDetails
              });
            } else {
              // If can't fetch blog details, use basic structure
              setJournalSection({
                ...sectionData,
                journals: sectionData.journals || []
              });
            }
          } catch (blogError) {
            console.error("Error fetching blog details:", blogError);
            // Fallback to basic structure
            setJournalSection({
              ...sectionData,
              journals: sectionData.journals || []
            });
          }
        } else {
          // No journals, set empty array
          setJournalSection({
            ...sectionData,
            journals: []
          });
        }
      } else {
        // If no journal section exists, create a default one
        setJournalSection({
          _id: "",
          sectionType: "journal",
          isVisible: true,
          sectionTitle: "Blog Features",
          journals: []
        });
      }
    } catch (error: any) {
      console.error("Error fetching journal section:", error);
      // Create default section on error
      setJournalSection({
        _id: "",
        sectionType: "journal",
        isVisible: true,
        sectionTitle: "Blog Features",
        journals: []
      });
      setError(error.response?.data?.message || error.message || "Failed to fetch journal section");
    } finally {
      setLoading(false);
    }
  };





  const handleAddJournals = async (selectedBlogs: any[]) => {
    if (!journalSection) return;

    try {
      // Replace all journals with the selected blogs (this handles both adding and removing)
      const updatedJournals = selectedBlogs.map((blog, index) => ({
        blogID: blog._id,
        position: index + 1
      }));

      // Prepare data in the format expected by the API
      const updateData = {
        sectionTitle: journalSection.sectionTitle,
        isVisible: journalSection.isVisible,
        journals: updatedJournals.map(journal => ({
          blogID: typeof journal.blogID === 'object' ? journal.blogID._id : journal.blogID,
          position: journal.position
        }))
      };

      const response = await JournalService.updateJournalSection(updateData);

      if (response.data && response.data.success) {
        toast.success("Blog features updated successfully");
        setRefreshKey(prev => prev + 1);
        setShowAddModal(false);
      } else {
        throw new Error(response.data?.message || "Failed to update blog features");
      }
    } catch (error: any) {
      console.error("Error updating blog features:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update blog features");
    }
  };

  const handleRemoveJournal = async () => {
    if (!journalSection || !selectedJournalId) return;

    try {
      const currentJournals = journalSection.journals || [];
      const updatedJournals = currentJournals.filter(
        journal => journal.blogID._id !== selectedJournalId
      );

      // Reorder positions
      const reorderedJournals = updatedJournals.map((journal, index) => ({
        ...journal,
        position: index + 1
      }));

      // Prepare data in the format expected by the API
      const updateData = {
        sectionTitle: journalSection.sectionTitle,
        isVisible: journalSection.isVisible,
        journals: reorderedJournals.map(journal => ({
          blogID: typeof journal.blogID === 'object' ? journal.blogID._id : journal.blogID,
          position: journal.position
        }))
      };

      const response = await JournalService.updateJournalSection(updateData);

      if (response.data && response.data.success) {
        toast.success("Journal removed successfully");
        setRefreshKey(prev => prev + 1);
        setShowDeleteModal(false);
        setSelectedJournalId(null);
      } else {
        throw new Error(response.data?.message || "Failed to remove journal");
      }
    } catch (error: any) {
      console.error("Error removing journal:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to remove journal");
    }
  };

  const confirmDelete = (journalId: string) => {
    setSelectedJournalId(journalId);
    setShowDeleteModal(true);
  };

  const handleToggleVisibility = async () => {
    if (!journalSection) return;

    try {
      const updatedSection = {
        ...journalSection,
        isVisible: !journalSection.isVisible
      };

      // Prepare data in the format expected by the API
      const updateData = {
        sectionTitle: updatedSection.sectionTitle,
        isVisible: updatedSection.isVisible,
        journals: updatedSection.journals.map(journal => ({
          blogID: typeof journal.blogID === 'object' ? journal.blogID._id : journal.blogID,
          position: journal.position
        }))
      };

      const response = await JournalService.updateJournalSection(updateData);

      if (response.data && response.data.success) {
        setJournalSection(updatedSection);
        toast.success(`Blog Features section ${updatedSection.isVisible ? 'enabled' : 'disabled'} on website`);
      } else {
        throw new Error(response.data?.message || "Failed to update visibility");
      }
    } catch (error: any) {
      console.error("Error updating visibility:", error);
      toast.error(error.response?.data?.message || error.message || "Failed to update visibility");
    }
  };

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: "400px" }}>
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Loading journal section...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center text-danger">
        <h5>Error loading journal section</h5>
        <p>{error}</p>
        <button
          onClick={() => setRefreshKey(prev => prev + 1)}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-8">
        {/* Header */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6 mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 sm:gap-6 mb-4 sm:mb-6">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">Blog Features</h1>
              <p className="text-gray-600 text-sm sm:text-base">Manage your featured blog articles and content</p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 w-full sm:w-auto">
              {/* Visibility Toggle */}
              <div className="flex items-center justify-between sm:justify-start gap-3 bg-gray-50 px-4 py-2 rounded-lg border">
                <span className="text-sm font-medium text-gray-700">
                  Show on Website
                </span>
                <button
                  onClick={handleToggleVisibility}
                  disabled={!journalSection}
                  className={`relative inline-flex items-center h-6 w-12 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed ${
                    journalSection?.isVisible ? "bg-blue-600" : "bg-gray-300"
                  }`}
                >
                  <span
                    className={`inline-block w-5 h-5 transform bg-white rounded-full transition-transform duration-200 ${
                      journalSection?.isVisible ? "translate-x-6" : "translate-x-1"
                    }`}
                  />
                </button>
              </div>

              {/* Add Blog Features Button */}
              <button
                onClick={() => setShowAddModal(true)}
                disabled={!journalSection}
                className="inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-sm w-full sm:w-auto"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                <span className="hidden sm:inline">Add Blog Features</span>
                <span className="sm:hidden">Add Features</span>
              </button>
            </div>
          </div>

          {/* Status Indicator */}
          {journalSection && (
            <div className={`px-4 py-2 rounded-lg text-sm font-medium ${
              journalSection.isVisible
                ? 'bg-green-50 text-green-700 border border-green-200'
                : 'bg-orange-50 text-orange-700 border border-orange-200'
            }`}>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  journalSection.isVisible ? 'bg-green-500' : 'bg-orange-500'
                }`}></div>
                <span>
                  {journalSection.isVisible
                    ? 'This section is currently visible on your website'
                    : 'This section is currently hidden from your website'
                  }
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Journal Content */}
        {journalSection && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            {journalSection.journals && journalSection.journals.length > 0 ? (
              <JournalTable
                journals={journalSection.journals}
                onRemove={confirmDelete}
              />
            ) : (
              <div className="text-center py-16">
                <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No blog features selected</h3>
                <p className="text-gray-500 mb-6">Get started by adding your first featured blog article</p>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-sm"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Add Your First Blog Feature
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Journal Modal */}
      <JournalAddModal
        show={showAddModal}
        onHide={() => setShowAddModal(false)}
        onAdd={handleAddJournals}
        existingJournals={journalSection?.journals || []}
      />



      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        show={showDeleteModal}
        onHide={() => {
          setShowDeleteModal(false);
          setSelectedJournalId(null);
        }}
        onConfirm={handleRemoveJournal}
        title="Remove Blog Feature"
        message="Are you sure you want to remove this blog feature from the section?"
        confirmText="Remove"
        type="danger"
      />
    </div>
  );
};



// Journal Table Component
const JournalTable = ({ journals, onRemove }: {
  journals: JournalEntry[];
  onRemove: (journalId: string) => void;
}) => {
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-3 sm:px-6 py-3 sm:py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
              Position
            </th>
            <th className="px-3 sm:px-6 py-3 sm:py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-80">
              Blog Name
            </th>
            <th className="hidden md:table-cell px-3 sm:px-6 py-3 sm:py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-40">
              Author
            </th>
            <th className="hidden sm:table-cell px-6 py-3 sm:py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
              Image
            </th>
            <th className="px-6 py-3 sm:py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {journals.map((journal) => (
            <tr key={journal.blogID._id} className="hover:bg-gray-50 transition-colors">
              <td className="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap w-20">
                <div className="flex items-center justify-center w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 text-blue-800 rounded-full text-xs sm:text-sm font-medium">
                  {journal.position}
                </div>
              </td>
              <td className="px-3 sm:px-6 py-3 sm:py-4 w-80">
                <div className="flex items-start space-x-2 sm:space-x-4">
                  {/* Show image and author on mobile in this column */}
                  <div className="sm:hidden flex-shrink-0">
                    {journal.blogID.featureImage ? (
                      <img
                        src={journal.blogID.featureImage}
                        alt={journal.blogID.blogName}
                        className="w-12 h-10 object-cover rounded border border-gray-200"
                      />
                    ) : (
                      <div className="w-12 h-10 bg-gray-100 rounded border border-gray-200 flex items-center justify-center">
                        <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {journal.blogID.blogName}
                    </p>
                    {/* Show author on mobile only */}
                    <p className="text-xs text-gray-500 mt-1 md:hidden">
                      {journal.blogID.writerName || "Unknown Author"}
                    </p>
                  </div>
                </div>
              </td>
              <td className="hidden md:table-cell px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap w-40">
                <p className="text-sm text-gray-900">
                  {journal.blogID.writerName || "Unknown Author"}
                </p>
              </td>
              <td className="hidden sm:table-cell px-6 py-3 sm:py-4 whitespace-nowrap w-24">
                {journal.blogID.featureImage ? (
                  <img
                    src={journal.blogID.featureImage}
                    alt={journal.blogID.blogName}
                    className="w-20 h-16 object-cover rounded-lg border border-gray-200 shadow-sm"
                  />
                ) : (
                  <div className="w-20 h-16 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </td>
              <td className="px-6 py-3 sm:py-4 whitespace-nowrap text-center w-20">
                <button
                  onClick={() => onRemove(journal.blogID._id)}
                  className="inline-flex items-center justify-center p-1.5 sm:p-2 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-lg transition-colors"
                  title="Remove"
                >
                  <FaTrash className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default Journal;
