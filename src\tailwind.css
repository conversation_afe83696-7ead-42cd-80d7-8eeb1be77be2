@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for blog section can be added here */
@layer components {
  .blog-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .blog-card {
    @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
  }

  .blog-title {
    @apply text-xl font-semibold text-gray-900 mb-2;
  }

  .blog-excerpt {
    @apply text-gray-600 text-sm line-clamp-3;
  }

  /* Loading spinner for image uploads */
  .loader {
    @apply inline-block w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin;
  }

  /* Text overflow utilities for long URLs and text */
  .text-overflow-wrap {
    @apply break-words overflow-wrap-anywhere;
  }

  .url-text {
    @apply break-all text-xs text-gray-500 max-w-full overflow-hidden;
  }
}
