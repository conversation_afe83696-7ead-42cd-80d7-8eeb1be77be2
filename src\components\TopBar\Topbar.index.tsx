import { Button, Container, Nav, Navbar, NavDropdown } from "react-bootstrap";
import { useLocation, useNavigate } from "react-router-dom";
import { useState } from "react";
import "./Topbar.css"

interface ITopbar {
  menuData: any;
}

export default function Topbar({ menuData }: ITopbar) {
  const navigate = useNavigate();
  const location = useLocation();
  const [showHomepageDropdown, setShowHomepageDropdown] = useState(false);

  const showAllowedMenu = menuData.filter(
    (routes: any) => routes.navbarShow === true
  );

  const handleLogout = () => {
    sessionStorage.removeItem("authKey");
    navigate("/login");
  };

  return (
    <>
      <Navbar
        expand="lg"
        className="mb-3 p-3 shadow"
      >
        <Container>
          <Navbar.Brand
            href="/dashboard"
            style={{
              fontSize: "20px",
            }}
          >
            TP Admin
          </Navbar.Brand>
          <Navbar.Toggle aria-controls="basic-navbar-nav" />
          <Navbar.Collapse
            id="basic-navbar-nav"
            className="justify-content-between"
          >
            <Nav className="mr-auto me-auto nav-links-font"> {/* Add the mr-auto class */}
              {showAllowedMenu.map((data: any, index: number) => {
                // Special handling for HomePage with dropdown
                if (data.name === "HomePage") {
                  return (
                    <div
                      key={index}
                      className="nav-item dropdown position-relative"
                      onMouseEnter={() => {
                        // Only show on hover for desktop
                        if (window.innerWidth >= 992) {
                          setShowHomepageDropdown(true);
                        }
                      }}
                      onMouseLeave={() => {
                        // Only hide on mouse leave for desktop
                        if (window.innerWidth >= 992) {
                          setShowHomepageDropdown(false);
                        }
                      }}
                      onClick={() => {
                        // Toggle on click for mobile
                        if (window.innerWidth < 992) {
                          setShowHomepageDropdown(!showHomepageDropdown);
                        }
                      }}
                    >
                      <Nav.Link
                        className={
                          "xrg-nav-link dropdown-toggle" +
                          (location.pathname.includes("/journal") || location.pathname.includes("/homepage")
                            ? " xrg-nav-selected"
                            : " ")
                        }
                        style={{ cursor: 'pointer' }}
                      >
                        HomePage
                      </Nav.Link>
                      <div
                        className={`dropdown-menu ${showHomepageDropdown ? 'show' : ''}`}
                        style={{
                          position: 'absolute',
                          top: '100%',
                          left: '0',
                          zIndex: 1000,
                          minWidth: '160px',
                          padding: '0.5rem 0',
                          margin: '0.125rem 0 0',
                          fontSize: '0.875rem',
                          color: '#212529',
                          textAlign: 'left',
                          backgroundColor: '#fff',
                          backgroundClip: 'padding-box',
                          border: '1px solid rgba(0,0,0,.15)',
                          borderRadius: '0.375rem',
                          boxShadow: '0 0.5rem 1rem rgba(0,0,0,.175)',
                          opacity: showHomepageDropdown ? 1 : 0,
                          visibility: showHomepageDropdown ? 'visible' : 'hidden',
                          transform: showHomepageDropdown ? 'translateY(0)' : 'translateY(-10px)',
                          transition: 'all 0.3s ease-in-out'
                        }}
                      >
                        <button
                          className="dropdown-item"
                          onClick={() => navigate("/journal")}
                          style={{
                            display: 'block',
                            width: '100%',
                            padding: '0.75rem 1.5rem',
                            clear: 'both',
                            fontWeight: 500,
                            color: '#374151',
                            textAlign: 'inherit',
                            textDecoration: 'none',
                            whiteSpace: 'nowrap',
                            backgroundColor: 'transparent',
                            border: 0,
                            cursor: 'pointer',
                            transition: 'all 0.2s ease-in-out'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f3f4f6';
                            e.currentTarget.style.color = '#1f2937';
                            e.currentTarget.style.transform = 'translateX(4px)';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'transparent';
                            e.currentTarget.style.color = '#374151';
                            e.currentTarget.style.transform = 'translateX(0)';
                          }}
                        >
                          Blog Features
                        </button>
                      </div>
                    </div>
                  );
                }

                // Regular navigation items
                return (
                  <div
                    key={index}
                    onClick={() => navigate(data.path)}
                  >
                    <Nav.Link className={
                      "xrg-nav-link" +
                      (data.path === location.pathname.split("/")[1]
                        ? " xrg-nav-selected"
                        : " ")
                    }>
                      {data.name}
                    </Nav.Link>
                  </div>
                );
              })}
            </Nav>
            <Button
              variant="secondary"
              className="text-white ml-2 justify-self-end"
              onClick={handleLogout}

            >
              Logout
            </Button>
          </Navbar.Collapse>
        </Container>
      </Navbar>
    </>
  );
}
