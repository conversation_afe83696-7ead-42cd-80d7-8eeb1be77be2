import { Navigate, Route, Routes } from "react-router-dom";
import PrivateRoutes from "./view/private/Private.index";
import Login from "./view/public/Login";
import Dashboard from "./view/private/Dashboard/Dashboard";
import Auth from "./components/Auth/auth";
import Therapist from "./view/private/Therapist/Therapist.index";
import Payments from "./view/private/Payments/payment.index";
import Appointment from "./view/private/Appointments/Appointment.index";
import TherapistDetails from "./view/private/Therapist/TherapistDetails";
import AppointmentDetails from "./view/private/Appointments/AppointmentDetails";
import Deduction from "./view/private/Deductions/Deductions.index";
import Payouts from "./view/private/Payouts/Payouts.index";
import Transactions from "./view/private/Transactions/Transactions.index";
import ClientTransaction from "./view/private/Client/ClientTransaction";
import DataExportIndex from "./view/private/DataExports/Dataexports.index";
import Subscription from "./view/private/Subscription/Subscription";
import Blog from "./view/private/Blog/Blog";
import CreateBlog from "./view/private/Blog/CreateBlog";
import ViewBlog from "./view/private/Blog/ViewBlog";
import Journal from "./view/private/Journal/Journal";


function PrivateRouter({ children }: any) {
    const auth = Auth.checkAuth();
    return auth ? <PrivateRoutes /> : <Navigate to="/login" />;
}


export default function MainRouter() {
    return (
        <>
            <Routes>
                <Route path="/" element={<PrivateRouter />}>
                    <Route path="/dashboard" element={<Dashboard />} />
                    <Route path="/homepage" element={<Journal />} />
                    <Route path="/invoice" element={<Payments />} />
                    <Route path="/appointments" element={<Appointment />} />
                    <Route path="/appointments/:id" element={<AppointmentDetails />} />
                    <Route path="/therapist/:id" element={<TherapistDetails />} />
                    <Route path="/therapist" element={<Therapist />} />
                    <Route path="/deduction" element={<Deduction />} />
                    <Route path="/payouts" element={<Payouts />} />
                    <Route path="/transactions" element={<Transactions />} />
                    <Route path="/client-transactions" element={<ClientTransaction />} />
                    <Route path="/data-exports" element={<DataExportIndex />} />
                    <Route path='/subscription' element={<Subscription/>}/>
                    <Route path='/blogs' element={<Blog/>}/>
                    <Route path='/blogs/create' element={<CreateBlog/>}/>
                    <Route path='/blogs/view/:id' element={<ViewBlog/>}/>
                    <Route path='/blogs/edit/:handle' element={<ViewBlog/>}/>
                    <Route path='/journal' element={<Journal/>}/>
                </Route>
                <Route path="/login" element={<Login />} />
                <Route path="*" element={<Navigate to="/login" />} />
            </Routes>
        </>
    )
}