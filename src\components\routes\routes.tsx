import { BsFillHdmiFill } from "react-icons/bs";
import IRouter from "../interfaces/IRouter";
import Dashboard from "../../view/private/Dashboard/Dashboard";
import Deduction from "../../view/private/Deductions/Deductions.index";
import Payouts from "../../view/private/Payouts/Payouts.index";
import Transactions from "../../view/private/Transactions/Transactions.index";
import ClientTransaction from "../../view/private/Client/ClientTransaction";
import DataExportIndex from "../../view/private/DataExports/Dataexports.index";
import Subscription from "../../view/private/Subscription/Subscription";
import Blog from "../../view/private/Blog/Blog";
import Journal from "../../view/private/Journal/Journal";


export const baseUrl = "/admin";

const adminRoutes: IRouter[] = [
  {
    path: "dashboard",
    navbarShow: true,
    element: Dashboard,
    name: "Dashboard",
    icon: BsFillHdmiFill,
  },
  {
    path: "therapist",
    navbarShow: true,
    element: Dashboard,
    name: "Therapist",
    icon: BsFillHdmiFill,
  },
  {
    path: "invoice",
    navbarShow: true,
    element: Dashboard,
    name: "Invoice",
    icon: BsFillHdmiFill,
  },
  // {
    //   path: "appointments",
    //   navbarShow: true,
    //   element: Dashboard,
    //   name: "Appointments",
    //   icon: BsFillHdmiFill,
    // },
    {
      path: "deduction",
      navbarShow: true,
      element: Deduction,
      name: "Deductions",
      icon: BsFillHdmiFill,
    },
    {
      path: "payouts",
      navbarShow: true,
      element: Payouts,
      name: "Payouts",
      icon: BsFillHdmiFill,
    },
    {
      path: "transactions",
      navbarShow: true,
      element: Transactions,
      name: "Transactions",
      icon: BsFillHdmiFill,
    },
    
    {
      path: "client-transactions",
      navbarShow: true,
      element: ClientTransaction,
      name: "Client Transactions",
      icon: BsFillHdmiFill,
    },
    
    {
      path: "data-exports",
      navbarShow: true,
      element: DataExportIndex,
      name: "Export",
      icon: BsFillHdmiFill,
    },
    {
      path: "subscription",
      navbarShow: true,
      element: Subscription,
      name: "Subscription",
      icon: BsFillHdmiFill,
  },
  {
    path: "blogs",
    navbarShow: true,
    element: Blog,
    name: "Blogs",
    icon: BsFillHdmiFill,
  },
  {
    path: "homepage",
    navbarShow: true,
    element: Journal,
    name: "HomePage",
    icon: BsFillHdmiFill,
  },
  
  // {
    //   path: "integrations",
  //   navbarShow: true,
  //   element: Dashboard,
  //   name: "Integrations",
  //   icon: BsFillHdmiFill,
  // },
  // {
  //   path: "settings",
  //   navbarShow: true,
  //   element: Dashboard,
  //   name: "Settings",
  //   icon: BsFillHdmiFill,
  // }

];
export default adminRoutes;
