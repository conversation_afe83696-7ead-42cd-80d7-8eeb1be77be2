
.xrg-nav-selected {
  position: relative;
  transition: all 0.3s ease-in-out;
}

  .xrg-nav-selected::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: calc(-1.25rem + .25rem);
    width: 100%;
    height: .25rem;
    background-color: #9fdea5;
    transition: width 0.3s ease-in-out;
    display: none; /* Hide on mobile by default */
  }

  /* Show green line only on desktop */
  @media (min-width: 768px) {
    .xrg-nav-selected::before {
      display: block;
    }
  }

/* Custom dropdown styles */
.nav-item.dropdown .dropdown-toggle::after {
  transition: transform 0.3s ease-in-out;
}

.nav-item.dropdown:hover .dropdown-toggle::after {
  transform: rotate(180deg);
}

/* Dropdown hover behavior for desktop */
@media (min-width: 992px) {
  .navbar-nav .nav-item.dropdown:hover .dropdown-menu {
    display: block;
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    transition: all 0.2s ease-in-out;
  }

  .navbar-nav .nav-item.dropdown .dropdown-menu {
    display: block;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease-in-out;
    margin-top: 0;
  }

  .navbar-nav .nav-item.dropdown:hover .dropdown-toggle::after {
    transform: rotate(180deg);
    transition: transform 0.2s ease-in-out;
  }
}