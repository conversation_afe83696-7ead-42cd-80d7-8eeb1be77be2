import React, { useState, useRef } from "react";
import ReactQuill from "react-quill";
import "react-quill/dist/quill.snow.css";
import { useNavigate } from "react-router-dom";
import toast from "react-hot-toast";
import ConfirmationModal from "../../../components/common/ConfirmationModal";
import { BlogService } from "../../../services/blog.service";


export default function CreateBlog() {
  const navigate = useNavigate();
  const coverFileInputRef = useRef<HTMLInputElement>(null);
  const writerFileInputRef = useRef<HTMLInputElement>(null);

  const [title, setTitle] = useState("");
  const [subheading, setSubheading] = useState("");
  const [content, setContent] = useState("");
  const [isPublished, setIsPublished] = useState(false);
  const [image, setImage] = useState("");
  const [imageError, setImageError] = useState("");
  const [writerName, setWriterName] = useState("");
  const [writerShortName, setWriterShortName] = useState("");
  const [writerDesignation, setWriterDesignation] = useState("");
  const [writerImage, setWriterImage] = useState("");
  const [writerImageError, setWriterImageError] = useState("");
  const [tag, setTag] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [saveError, setSaveError] = useState("");
  const [coverImageLoading, setCoverImageLoading] = useState(false);
  const [writerImageLoading, setWriterImageLoading] = useState(false);
  const [createModal, setCreateModal] = useState({
    isOpen: false,
    loading: false
  });

  const uploadImageToS3 = async (file: File, imageType: string = "blog") => {
    try {
      let response;
      if (imageType === "writer") {
        response = await BlogService.uploadWriterImage(file);
      } else {
        response = await BlogService.uploadBlogImage(file);
      }

      if (response.data && response.data.success) {
        const imageUrl = response.data.data?.url || response.data.url || response.data.data?.fileUrl || response.data.data;

        return {
          success: true,
          url: imageUrl
        };
      } else {
        return {
          success: false,
          message: response.data?.message || "Upload failed"
        };
      }
    } catch (error: any) {
      console.error("File upload error:", error);
      return {
        success: false,
        message: error.response?.data?.message || "Upload failed"
      };
    }
  };

  // Handle Image Upload
  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
    setImageFunction: (url: string) => void,
    triggerBy: string
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      setImageError("File size should not exceed 5MB");
      return;
    }

    if (triggerBy === "coverImage") setCoverImageLoading(true);
    if (triggerBy === "writerImage") setWriterImageLoading(true);

    const imageType = triggerBy === "writerImage" ? "writer" : "blog";

    const imageUrl = await uploadImageToS3(file, imageType);

    if (imageUrl.success) {
      setImageFunction(imageUrl.url);
      setImageError("");
      setWriterImageError("");
    } else {
      if (triggerBy === "coverImage") {
        setImageError(imageUrl.message);
      } else if (triggerBy === "writerImage") {
        setWriterImageError(imageUrl.message);
      }
    }

    if (triggerBy === "coverImage") setCoverImageLoading(false);
    if (triggerBy === "writerImage") setWriterImageLoading(false);
  };

  // Handle Tag Addition
  const handleAddTag = () => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      setTags([...tags, trimmedTag]);
      setTag("");
      setShowDropdown(false);
    }
  };

  // Handle Enter key press for tag input
  const handleTagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
  };

  // Handle Tag Removal
  const handleRemoveTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  // Handle Image Removal
  const handleRemoveImage = (setImageFunction: (url: string) => void, triggerBy: string) => {
    setImageFunction("");
    if (triggerBy === "coverImage" && coverFileInputRef.current) {
      coverFileInputRef.current.value = "";
    } else if (triggerBy === "writerImage" && writerFileInputRef.current) {
      writerFileInputRef.current.value = "";
    }
  };

  const getFormattedDate = () => {
    const today = new Date();
    const day = String(today.getDate()).padStart(2, "0");
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const year = today.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Handle Form Submission
  const handleSave = () => {
    // Basic validation
    if (!title.trim()) {
      setSaveError("Blog title is required.");
      return;
    }
    if (!content.trim()) {
      setSaveError("Blog content is required.");
      return;
    }
    setSaveError("");
    setCreateModal({ isOpen: true, loading: false });
  };

  const handleCreateConfirm = async () => {
    setCreateModal(prev => ({ ...prev, loading: true }));

    const publishDate = getFormattedDate();
    const formData = {
      blogName: title,
      blogSubheading: subheading,
      description: content,
      isVisible: isPublished,
      featureImage: image,
      writerName,
      writerShortname: writerShortName,
      writerDesignation: writerDesignation,
      writerImage,
      tag: tags,
      publishDate,
    };

    try {
      const response = await BlogService.createBlog(formData);

      if (response.data && response.data.success) {
        toast.success(response.data.message || "Blog post created successfully!");
        setCreateModal({ isOpen: false, loading: false });
        navigate(`/blogs`);
      } else {
        throw new Error(response.data?.message || "Failed to create blog post");
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "Failed to create blog post";
      setSaveError(errorMessage);
      toast.error(errorMessage);
      setCreateModal({ isOpen: false, loading: false });
    }
  };

  const handleCreateCancel = () => {
    setCreateModal({ isOpen: false, loading: false });
  };

  // Handle title change
  const handleTitleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTitle(e.target.value);
  };

  return (
    <div className="max-w-5xl mx-auto p-3 sm:p-6 space-y-6">
      {/* Page Title and Save Button */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0">
        <h1 className="text-xl sm:text-2xl font-semibold text-left">Create Blog Post</h1>
        <button
          onClick={handleSave}
          className="w-full sm:w-auto p-3 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700"
        >
          Create Post
        </button>
      </div>

      {/* Show the error if save failure */}
      {saveError && (
        <div className="bg-red-100 border border-red-400 text-red-700 p-3 rounded-md mt-2">
          <p className="text-sm">{saveError}</p>
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
        {/* Left Section */}
        <div className="col-span-1 sm:col-span-2 space-y-6">
          {/* Title & Content Card */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-6 min-h-[650px]">
            {/* Title Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Title
              </label>
              <input
                type="text"
                placeholder="Give your blog post a title"
                value={title}
                onChange={handleTitleChange}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Subheading Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Subheading
              </label>
              <textarea
                placeholder="Add a brief subheading (3-4 lines)"
                value={subheading}
                onChange={(e) => setSubheading(e.target.value)}
                rows={3}
                maxLength={300}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 resize-none"
              />
              <p className="text-xs text-gray-500 mt-1">
                {subheading.length}/300 characters
              </p>
            </div>

            {/* Content Editor */}
            <div className="mb-20">
              <label className="block text-sm font-medium text-gray-700 text-left">
                Content
              </label>
              <ReactQuill
                theme="snow"
                value={content}
                onChange={setContent}
                className="mt-1"
                style={{ height: "24em", marginBottom: "4rem" }}
              />
            </div>
          </div>


        </div>

        {/* Right Section */}
        <div className="space-y-6">
          {/* Visibility Toggle */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h2 className="text-lg font-medium text-left">Visibility</h2>
            <div className="flex items-center justify-between">
              <span>{isPublished ? "Visible" : "Hidden"}</span>
              <button
                onClick={() => setIsPublished(!isPublished)}
                className={`relative inline-flex items-center h-6 w-12 rounded-full transition ${
                  isPublished ? "bg-green-500" : "bg-gray-300"
                }`}
              >
                <span
                  className={`inline-block w-5 h-5 transform bg-white rounded-full transition ${
                    isPublished ? "translate-x-6" : "translate-x-1"
                  }`}
                ></span>
              </button>
            </div>
          </div>

          {/* Image Upload */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h2 className="text-lg font-medium text-left">Blog Cover Image</h2>
            <input
              type="file"
              accept="image/*"
              onChange={(e) => handleImageChange(e, setImage, "coverImage")}
              className="hidden"
              id="coverImageUpload"
              ref={coverFileInputRef}
            />
            <div className="text-left">
              <label
                htmlFor="coverImageUpload"
                className="inline-block bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium shadow-sm
              hover:bg-gray-900 hover:text-white cursor-pointer transition duration-200"
              >
                Choose Image
              </label>
            </div>


            {image && !coverImageLoading && (
              <div className="relative w-fit">
                <img
                  src={image}
                  alt="Blog Cover"
                  className="w-20 h-20 object-cover rounded-md"
                  onError={() => {
                    // Handle image load error silently
                  }}
                  onLoad={() => {
                    // Handle image load success silently
                  }}
                />
                <button
                  onClick={() => handleRemoveImage(setImage, "coverImage")}
                  className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 bg-gray-500 text-white text-xs rounded-full p-1 shadow-md"
                >
                  &times;
                </button>
              </div>
            )}
            {coverImageLoading && (
              <div className="flex items-center mt-2">
                <span className="loader mr-2"></span>
                <span className="text-blue-500 text-sm">Uploading image...</span>
              </div>
            )}
            <span className="text-[12px] text-left text-[#353535] mt-1">(Image size: 1200x600)</span>
            {imageError && <p className="text-red-500 text-sm">{imageError}</p>}
          </div>

          {/* Writer Details */}
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-sm border border-gray-200 space-y-4">
            <h2 className="text-lg font-medium text-left">Writer Details</h2>

            {/* Writer Name */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Writer Name
              </label>
              <input
                type="text"
                placeholder="Enter writer name"
                value={writerName}
                onChange={(e) => setWriterName(e.target.value)}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Writer Short Description */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Writer Short Description
              </label>
              <input
                type="text"
                placeholder="Enter writer short description"
                value={writerShortName}
                onChange={(e) => setWriterShortName(e.target.value)}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Writer Designation */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Writer Designation
              </label>
              <input
                type="text"
                placeholder="Enter writer designation"
                value={writerDesignation}
                onChange={(e) => setWriterDesignation(e.target.value)}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Writer Image */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Writer Image
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => handleImageChange(e, setWriterImage, "writerImage")}
                className="hidden"
                id="writerImageUpload"
                ref={writerFileInputRef}
              />
              <div className="text-left">
                <label
                  htmlFor="writerImageUpload"
                  className="inline-block bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium shadow-sm
              hover:bg-gray-900 hover:text-white cursor-pointer transition duration-200"
                >
                  Choose Image
                </label>
              </div>
              {writerImage && !writerImageLoading && (
                <div className="relative w-fit mt-4">
                  <img
                    src={writerImage}
                    alt="Writer"
                    className="w-20 h-20 object-cover rounded-full mt-2"
                  />
                  <button
                    onClick={() => handleRemoveImage(setWriterImage, "writerImage")}
                    className="absolute top-0 right-0 -translate-y-1/2 translate-x-1/2 bg-gray-500 text-white text-xs rounded-full p-1 shadow-md"
                  >
                    &times;
                  </button>
                  <span className="text-[12px] text-left text-[#353535] mt-1">(Image size: 300x300)</span>
                </div>
              )}
              {writerImageLoading && (
                <div className="flex items-center mt-2">
                  <span className="loader mr-2"></span>
                  <span className="text-blue-500 text-sm">Uploading image...</span>
                </div>
              )}
              {writerImageError && (
                <p className="text-red-500 text-sm">{writerImageError}</p>
              )}
            </div>

            {/* Tag Input & Display */}
            <div>
              <label className="block text-sm font-medium text-gray-700 text-left">
                Tag
              </label>
              <input
                type="text"
                placeholder="Enter tag and press Enter or click Add"
                value={tag}
                onChange={(e) => {
                  setTag(e.target.value);
                  setShowDropdown(e.target.value.trim().length > 0);
                }}
                onKeyDown={handleTagKeyDown}
                onFocus={() => setShowDropdown(tag.trim().length > 0)}
                onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
                className="w-full mt-1 p-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
              />

              {/* Tag Dropdown */}
              {showDropdown && tag.trim().length > 0 && !tags.includes(tag.trim()) && (
                <div className="bg-white border border-gray-300 rounded-md mt-1 shadow-md z-10">
                  <button
                    type="button"
                    className="w-full text-left p-2 hover:bg-gray-100 focus:bg-gray-100 focus:outline-none"
                    onClick={handleAddTag}
                  >
                    Add "{tag.trim()}"
                  </button>
                </div>
              )}

              {/* Display Tags */}
              <div className="mt-2 flex flex-wrap gap-2">
                {tags.map((tagItem, index) => (
                  <span
                    key={index}
                    className="flex items-center px-3 py-1 rounded-full border border-gray-300 bg-gray-100 text-gray-700 text-sm"
                  >
                    {tagItem}
                    <button
                      onClick={() => handleRemoveTag(index)}
                      className="ml-2 text-gray-500 hover:text-gray-700"
                    >
                      &times;
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </div>

          <button
            onClick={() => navigate("/blogs")}
            className="w-full p-3 bg-gray-600 text-white font-medium rounded-md hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {/* Create Confirmation Modal */}
      <ConfirmationModal
        show={createModal.isOpen}
        onHide={handleCreateCancel}
        onConfirm={handleCreateConfirm}
        title="Create Blog Post"
        message="Are you sure you want to create this blog post?"
        confirmText="Create Post"
        cancelText="Cancel"
        type="info"
        loading={createModal.loading}
      />
    </div>
  );
}
