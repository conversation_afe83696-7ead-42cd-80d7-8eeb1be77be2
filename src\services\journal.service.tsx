import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class JournalService {
  // Get journal section data
  static async getJournalSection() {
    const response = await makeRequest("admin/homepage-sections/type/journal", RequestMethods.GET);
    return response;
  }

  // Update journal section with selected blogs
  static async updateJournalSection(journalData: any) {
    const response = await makeRequest("admin/homepage-sections/type/journal", RequestMethods.PUT, journalData);
    return response;
  }

  // Get all blogs for journal selection
  static async getAllBlogsForSelection() {
    const response = await makeRequest("admin/blogs", RequestMethods.GET);
    return response;
  }

  // Get public homepage sections (for visibility check)
  static async getPublicHomepageSections() {
    const response = await makeRequest("/homepage-sections/public", RequestMethods.GET);
    return response;
  }
}
