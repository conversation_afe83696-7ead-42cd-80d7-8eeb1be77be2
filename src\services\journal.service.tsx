import makeRequest from "../api/makeRequest";
import { RequestMethods } from "../api/requestMethode";

export class JournalService {
  // Get journal section data
  static async getJournalSection() {
    const response = await makeRequest("admin/homepage-sections/type/journal", RequestMethods.GET);
    return response;
  }

  // Update journal section with selected blogs
  static async updateJournalSection(journalData: any) {
    const response = await makeRequest("admin/homepage-sections/type/journal", RequestMethods.PUT, journalData);
    return response;
  }

  // Get all blogs for journal selection
  static async getAllBlogsForSelection() {
    const response = await makeRequest("admin/blogs", RequestMethods.GET);
    return response;
  }

  // Get public homepage sections (for visibility check)
  static async getPublicHomepageSections() {
    const response = await makeRequest("/homepage-sections/public", RequestMethods.GET);
    return response;
  }

  // Remove a specific blog from journal section by blog ID
  static async removeBlogFromJournal(blogId: string) {
    try {
      // First get current journal section
      const currentSection = await this.getJournalSection();

      if (currentSection.data && currentSection.data.success && currentSection.data.data) {
        const journalSection = currentSection.data.data;
        const currentJournals = journalSection.journals || [];

        // Filter out the blog to be removed
        const updatedJournals = currentJournals.filter(
          (journal: any) => journal.blogID._id !== blogId
        );

        // Update positions to be sequential
        const journalsWithUpdatedPositions = updatedJournals.map((journal: any, index: number) => ({
          ...journal,
          position: index + 1
        }));

        // Update the journal section - format data like in Journal.tsx
        const updateData = {
          sectionTitle: journalSection.sectionTitle,
          isVisible: journalSection.isVisible,
          journals: journalsWithUpdatedPositions.map((journal: any) => ({
            blogID: typeof journal.blogID === 'object' ? journal.blogID._id : journal.blogID,
            position: journal.position
          }))
        };

        return await this.updateJournalSection(updateData);
      }

      return { data: { success: true, message: "Blog not found in journal section" } };
    } catch (error) {
      console.error("Error removing blog from journal:", error);
      throw error;
    }
  }
}
